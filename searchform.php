<form role="search" method="get" class="search-form flex items-center w-full" action="<?php echo esc_url( home_url( '/' ) ); ?>">
	<label for="search-form-input" class="sr-only"><?php echo _x( 'Search for:', 'label', 'lwos-2025' ); ?></label>
	<input type="search"
		   id="search-form-input"
		   class="search-field p-2 border border-gray-700 bg-gray-800 text-white placeholder-gray-400 focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm rounded-md"
		   placeholder="<?php echo esc_attr_x( 'Search &hellip;', 'placeholder', 'lwos-2025' ); ?>"
		   value="<?php echo get_search_query(); ?>"
		   name="s"
	/>
	<button type="submit" class="search-submit ml-2 p-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
		<svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
			<path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
		</svg>
		<span class="sr-only"><?php echo _x( 'Search', 'submit button', 'lwos-2025' ); ?></span>
	</button>
</form> 